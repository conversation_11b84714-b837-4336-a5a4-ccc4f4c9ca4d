#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to prepare Nepali dataset for F5-TTS fine-tuning.
Converts from Thorsten format to F5-TTS format and prepares the dataset.
"""

import os
import sys
import shutil
import pandas as pd
from pathlib import Path
import argparse

def convert_thorsten_to_f5tts_format(input_dir, output_dir):
    """
    Convert Thorsten format dataset to F5-TTS format.
    
    Thorsten format: filename|text|normalized_text
    F5-TTS format: filename|text (without path, just filename)
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory structure
    output_path.mkdir(parents=True, exist_ok=True)
    wavs_output = output_path / "wavs"
    wavs_output.mkdir(exist_ok=True)
    
    # Read the original metadata
    metadata_file = input_path / "metadata.csv"
    if not metadata_file.exists():
        raise FileNotFoundError(f"Metadata file not found: {metadata_file}")
    
    print(f"Reading metadata from: {metadata_file}")
    
    # Read the CSV with proper handling
    with open(metadata_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Process each line and create new metadata
    new_metadata = []
    copied_files = 0
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        parts = line.split('|')
        if len(parts) < 2:
            print(f"Warning: Line {line_num} has insufficient parts: {line}")
            continue
            
        filename = parts[0].strip()
        text = parts[1].strip()

        # Handle both cases: with and without .wav extension
        if not filename.endswith('.wav'):
            audio_filename = filename + '.wav'
        else:
            audio_filename = filename

        # Source audio file path
        source_audio = input_path / "wavs" / audio_filename
        
        if not source_audio.exists():
            print(f"Warning: Audio file not found: {source_audio}")
            continue
            
        # Copy audio file to output directory
        dest_audio = wavs_output / audio_filename
        try:
            shutil.copy2(source_audio, dest_audio)
            copied_files += 1

            # Add to new metadata (F5-TTS format: filename|text)
            # Use the audio filename (with .wav extension) for F5-TTS
            new_metadata.append(f"{audio_filename}|{text}")
            
        except Exception as e:
            print(f"Error copying {source_audio}: {e}")
            continue
    
    # Write new metadata file
    output_metadata = output_path / "metadata.csv"
    with open(output_metadata, 'w', encoding='utf-8') as f:
        for entry in new_metadata:
            f.write(entry + '\n')
    
    print(f"Conversion completed!")
    print(f"- Copied {copied_files} audio files")
    print(f"- Created metadata with {len(new_metadata)} entries")
    print(f"- Output directory: {output_path}")
    
    return output_path

def prepare_f5tts_dataset(dataset_dir, output_dir, num_workers=None):
    """
    Prepare the dataset for F5-TTS training using the official preparation script.
    """
    print(f"Preparing F5-TTS dataset from: {dataset_dir}")
    print(f"Output will be saved to: {output_dir}")

    # Import the F5-TTS preparation function
    sys.path.append(os.path.dirname(__file__))

    try:
        # Import required modules
        from f5_tts.train.datasets.prepare_csv_wavs import prepare_csv_wavs_dir, save_prepped_dataset
        import shutil
        from pathlib import Path

        # Find the available vocab file
        vocab_file = Path("./f5-tts-env/lib/python3.10/site-packages/f5_tts/infer/examples/vocab.txt")
        if not vocab_file.exists():
            print(f"Warning: Vocab file not found at {vocab_file}")
            print("Creating a basic vocab file for Nepali text...")
            # We'll create our own vocab file from the dataset
            vocab_file = None

        # Prepare the dataset
        sub_result, durations, vocab_set = prepare_csv_wavs_dir(dataset_dir, num_workers=num_workers)

        # Save the prepared dataset
        save_prepped_dataset(output_dir, sub_result, durations, vocab_set, is_finetune=True)

        # If we have a vocab file, copy it to the output directory
        if vocab_file and vocab_file.exists():
            output_vocab = Path(output_dir) / "vocab.txt"
            shutil.copy2(vocab_file, output_vocab)
            print(f"Copied vocab file to: {output_vocab}")

        print(f"Dataset preparation completed! Prepared dataset saved to: {output_dir}")

    except ImportError as e:
        print(f"Error importing F5-TTS modules: {e}")
        print("Make sure F5-TTS is properly installed.")
        return False
    except Exception as e:
        print(f"Error during dataset preparation: {e}")
        return False

    return True

def main():
    parser = argparse.ArgumentParser(description="Prepare Nepali dataset for F5-TTS fine-tuning")
    parser.add_argument("--input_dir", type=str, default="nepali_dataset_thorsten_format",
                       help="Input directory with Thorsten format dataset")
    parser.add_argument("--converted_dir", type=str, default="nepali_dataset_f5tts_format",
                       help="Intermediate directory for F5-TTS format dataset")
    parser.add_argument("--output_dir", type=str, default="nepali_dataset_prepared",
                       help="Final output directory for prepared dataset")
    parser.add_argument("--workers", type=int, default=None,
                       help="Number of worker processes for dataset preparation")
    parser.add_argument("--skip_conversion", action="store_true",
                       help="Skip conversion step if already done")
    
    args = parser.parse_args()
    
    try:
        # Step 1: Convert format if needed
        if not args.skip_conversion:
            print("Step 1: Converting Thorsten format to F5-TTS format...")
            converted_path = convert_thorsten_to_f5tts_format(args.input_dir, args.converted_dir)
        else:
            converted_path = Path(args.converted_dir)
            print(f"Step 1: Skipped conversion, using existing directory: {converted_path}")
        
        # Step 2: Prepare dataset for F5-TTS
        print("\nStep 2: Preparing dataset for F5-TTS training...")
        success = prepare_f5tts_dataset(converted_path, args.output_dir, args.workers)
        
        if success:
            print(f"\n✅ Dataset preparation completed successfully!")
            print(f"📁 Prepared dataset location: {args.output_dir}")
            print(f"🎯 Ready for F5-TTS fine-tuning!")
        else:
            print(f"\n❌ Dataset preparation failed!")
            return 1
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
