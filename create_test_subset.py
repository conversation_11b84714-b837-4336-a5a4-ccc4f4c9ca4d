#!/usr/bin/env python3
"""
Create a small test subset for F5-TTS preparation testing.
"""

import os
import shutil
from pathlib import Path

def create_test_subset(source_dir, output_dir, num_samples=100):
    """
    Create a small test subset of the dataset.
    """
    source_path = Path(source_dir)
    output_path = Path(output_dir)
    
    # Create output directory structure
    output_path.mkdir(parents=True, exist_ok=True)
    wavs_output = output_path / "wavs"
    wavs_output.mkdir(exist_ok=True)
    
    # Read metadata and take first N samples
    metadata_file = source_path / "metadata.csv"
    test_metadata = []
    copied_files = 0
    
    with open(metadata_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= num_samples:
                break
                
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('|')
            if len(parts) < 2:
                continue
                
            filename = parts[0].strip()
            
            # Source and destination paths
            source_audio = source_path / "wavs" / filename
            dest_audio = wavs_output / filename
            
            if source_audio.exists():
                try:
                    shutil.copy2(source_audio, dest_audio)
                    test_metadata.append(line)
                    copied_files += 1
                except Exception as e:
                    print(f"Error copying {source_audio}: {e}")
    
    # Write test metadata
    test_metadata_file = output_path / "metadata.csv"
    with open(test_metadata_file, 'w', encoding='utf-8') as f:
        for entry in test_metadata:
            f.write(entry + '\n')
    
    print(f"Created test subset with {copied_files} files")
    print(f"Test dataset location: {output_path}")
    
    return output_path

if __name__ == "__main__":
    test_dir = create_test_subset(
        source_dir="nepali_dataset_f5tts_format",
        output_dir="nepali_test_subset",
        num_samples=100
    )
    print(f"✅ Test subset created at: {test_dir}")
