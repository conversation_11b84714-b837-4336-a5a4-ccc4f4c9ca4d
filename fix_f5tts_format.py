#!/usr/bin/env python3
"""
Fix the dataset format for F5-TTS by moving audio files to the correct location
and adding a proper header to the metadata.csv file.
"""

import os
import shutil
from pathlib import Path

def fix_f5tts_format(dataset_dir):
    """
    Fix the dataset format for F5-TTS:
    1. Move audio files from wavs/ subdirectory to the main directory
    2. Add a header to metadata.csv
    3. Update paths in metadata.csv if needed
    """
    dataset_path = Path(dataset_dir)
    wavs_dir = dataset_path / "wavs"
    metadata_file = dataset_path / "metadata.csv"
    
    if not wavs_dir.exists():
        print(f"No wavs directory found in {dataset_dir}")
        return False
    
    if not metadata_file.exists():
        print(f"No metadata.csv found in {dataset_dir}")
        return False
    
    print(f"Fixing F5-TTS format for: {dataset_dir}")
    
    # Step 1: Move audio files from wavs/ to main directory
    moved_files = 0
    for audio_file in wavs_dir.glob("*.wav"):
        dest_file = dataset_path / audio_file.name
        try:
            shutil.move(str(audio_file), str(dest_file))
            moved_files += 1
        except Exception as e:
            print(f"Error moving {audio_file}: {e}")
    
    print(f"Moved {moved_files} audio files to main directory")
    
    # Step 2: Remove empty wavs directory
    try:
        wavs_dir.rmdir()
        print("Removed empty wavs directory")
    except Exception as e:
        print(f"Could not remove wavs directory: {e}")
    
    # Step 3: Add header to metadata.csv and ensure proper format
    # Read existing metadata
    with open(metadata_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Write with header
    with open(metadata_file, 'w', encoding='utf-8') as f:
        # Add header
        f.write("audio_path|text\n")
        # Write existing data
        for line in lines:
            f.write(line)
    
    print(f"Added header to metadata.csv")
    print(f"✅ F5-TTS format fixed for {dataset_dir}")
    
    return True

if __name__ == "__main__":
    # Fix the test subset
    success = fix_f5tts_format("nepali_test_subset")
    
    if success:
        print("\n🎯 Test subset is now ready for F5-TTS!")
    else:
        print("\n❌ Failed to fix test subset format")
