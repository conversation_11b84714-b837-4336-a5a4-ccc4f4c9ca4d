#!/usr/bin/env python3
"""
Revert the changes and fix the F5-TTS format properly.
"""

import os
import shutil
from pathlib import Path

def revert_and_fix_format(dataset_dir):
    """
    Revert changes and fix F5-TTS format properly:
    1. Create wavs directory
    2. Move audio files back to wavs directory  
    3. Fix metadata.csv to have proper format with wavs/ prefix
    """
    dataset_path = Path(dataset_dir)
    metadata_file = dataset_path / "metadata.csv"
    wavs_dir = dataset_path / "wavs"
    
    print(f"Reverting and fixing F5-TTS format for: {dataset_dir}")
    
    # Step 1: Create wavs directory
    wavs_dir.mkdir(exist_ok=True)
    
    # Step 2: Move audio files back to wavs directory
    moved_files = 0
    for audio_file in dataset_path.glob("*.wav"):
        dest_file = wavs_dir / audio_file.name
        try:
            shutil.move(str(audio_file), str(dest_file))
            moved_files += 1
        except Exception as e:
            print(f"Error moving {audio_file}: {e}")
    
    print(f"Moved {moved_files} audio files back to wavs directory")
    
    # Step 3: Fix metadata.csv format
    if metadata_file.exists():
        # Read existing metadata (skip the header we added)
        with open(metadata_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Remove header if it exists
        if lines and lines[0].strip().lower().startswith('audio_path'):
            lines = lines[1:]
        
        # Write corrected metadata with wavs/ prefix
        with open(metadata_file, 'w', encoding='utf-8') as f:
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split('|')
                if len(parts) >= 2:
                    filename = parts[0].strip()
                    text = parts[1].strip()
                    
                    # Add wavs/ prefix if not already present
                    if not filename.startswith('wavs/'):
                        filename = f"wavs/{filename}"
                    
                    f.write(f"{filename}|{text}\n")
    
    print(f"Fixed metadata.csv format with wavs/ prefix")
    print(f"✅ F5-TTS format properly fixed for {dataset_dir}")
    
    return True

if __name__ == "__main__":
    # Fix the test subset
    success = revert_and_fix_format("nepali_test_subset")
    
    if success:
        print("\n🎯 Test subset is now properly formatted for F5-TTS!")
    else:
        print("\n❌ Failed to fix test subset format")
