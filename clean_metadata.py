#!/usr/bin/env python3
"""
Script to clean metadata file to only include entries with existing audio files.
"""

import os
from pathlib import Path

def clean_metadata(metadata_file, wavs_dir, output_file):
    """
    Clean metadata file to only include entries with existing audio files.
    """
    wavs_path = Path(wavs_dir)
    
    # Get list of existing audio files
    existing_files = set()
    for audio_file in wavs_path.glob("*.wav"):
        existing_files.add(audio_file.name)
    
    print(f"Found {len(existing_files)} audio files in {wavs_dir}")
    
    # Read metadata and filter
    cleaned_entries = []
    total_entries = 0
    
    with open(metadata_file, 'r', encoding='utf-8') as f:
        for line in f:
            total_entries += 1
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('|')
            if len(parts) < 2:
                continue
                
            filename = parts[0].strip()
            
            if filename in existing_files:
                cleaned_entries.append(line)
    
    # Write cleaned metadata
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in cleaned_entries:
            f.write(entry + '\n')
    
    print(f"Original metadata entries: {total_entries}")
    print(f"Cleaned metadata entries: {len(cleaned_entries)}")
    print(f"Cleaned metadata saved to: {output_file}")
    
    return len(cleaned_entries)

if __name__ == "__main__":
    # Clean the F5-TTS format metadata
    result = clean_metadata(
        metadata_file="nepali_dataset_f5tts_format/metadata.csv",
        wavs_dir="nepali_dataset_f5tts_format/wavs",
        output_file="nepali_dataset_f5tts_format/metadata_cleaned.csv"
    )
    
    # Replace the original metadata with cleaned version
    if result > 0:
        os.rename("nepali_dataset_f5tts_format/metadata_cleaned.csv", 
                 "nepali_dataset_f5tts_format/metadata.csv")
        print("✅ Metadata file cleaned and replaced!")
    else:
        print("❌ No matching entries found!")
